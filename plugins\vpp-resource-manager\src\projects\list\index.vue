<template>
  <div class="list-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <el-form :inline="true" :model="searchForm" class="search-form-inline">
          <el-form-item>
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入关键字"
              clearable
              style="width: 300px"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-select v-model="searchForm.category" placeholder="全部" clearable style="width: 120px">
              <el-option label="全部" value=""></el-option>
              <el-option label="类型1" value="type1"></el-option>
              <el-option label="类型2" value="type2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
        size="small"
      >
        <el-table-column
          prop="index"
          label="序号"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.$index + 1 + (currentPage - 1) * pageSize }}
          </template>
        </el-table-column>

        <el-table-column
          prop="computerGroup"
          label="计算机机组"
          min-width="150"
          align="center"
        ></el-table-column>

        <el-table-column
          prop="unitPosition"
          label="机组机位"
          min-width="120"
          align="center"
        ></el-table-column>

        <el-table-column
          prop="dataSourceCount"
          label="数据源数量"
          width="120"
          align="center"
        ></el-table-column>

        <el-table-column
          label="操作"
          width="180"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleDetail(scope.row)"
              class="action-btn detail-btn"
            >
              详情
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleEdit(scope.row)"
              class="action-btn edit-btn"
            >
              编辑
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row)"
              class="action-btn delete-btn"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: "ListPage",
  data() {
    return {
      loading: false,
      searchForm: {
        keyword: "",
        category: ""
      },
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0
    };
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 加载数据
    loadData() {
      this.loading = true;
      // 模拟API调用
      setTimeout(() => {
        const mockData = [];
        for (let i = 1; i <= 50; i++) {
          mockData.push({
            id: i,
            computerGroup: `计算机机组${String(i).padStart(2, '0')}`,
            unitPosition: `机组机位${i}`,
            dataSourceCount: Math.floor(Math.random() * 10),
            status: Math.random() > 0.5 ? '正常' : '异常'
          });
        }
        this.tableData = mockData.slice(
          (this.currentPage - 1) * this.pageSize,
          this.currentPage * this.pageSize
        );
        this.total = mockData.length;
        this.loading = false;
      }, 500);
    },
    
    // 搜索
    handleSearch() {
      this.currentPage = 1;
      this.loadData();
    },
    
    // 详情
    handleDetail(row) {
      this.$message.info(`查看详情: ${row.computerGroup}`);
    },
    
    // 编辑
    handleEdit(row) {
      this.$message.info(`编辑: ${row.computerGroup}`);
    },
    
    // 删除
    handleDelete(row) {
      this.$confirm(`确定要删除 ${row.computerGroup} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功');
        this.loadData();
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    
    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.loadData();
    },
    
    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val;
      this.loadData();
    }
  }
};
</script>

<style scoped>
.list-container {
  display: flex;
  width: 1664px;
  height: 833px;
  padding: 20px;
  align-items: flex-start;
  gap: 16px;
  flex-shrink: 0;
  background: var(--color-Fill-fill-color, #F0F2F5);
  flex-direction: column;
}

.search-section {
  width: 100%;
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form-inline {
  margin: 0;
}

.search-form-inline .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.table-section {
  flex: 1;
  width: 100%;
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.action-btn {
  margin: 0 2px;
  padding: 2px 6px;
  font-size: 12px;
}

.detail-btn {
  color: #409EFF;
}

.edit-btn {
  color: #67C23A;
}

.delete-btn {
  color: #F56C6C;
}

.pagination-section {
  width: 100%;
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: right;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .list-container {
    width: 100%;
    height: auto;
    min-height: 100vh;
  }
}
</style>



## 📋 项目简介


### 🛠 技术栈

- 前端框架: Vue 2.7 + Vue Router + Vuex
- UI 组件库: Element UI + @omega 系列组件
- 样式方案: Tailwind CSS + SCSS
- 图表库: cet-chart + @omega/trend
- 构建工具: Vue CLI 5.x + Webpack
- 包管理: pnpm 工作区
- 代码规范: ESLint + Prettier

### 📦 环境要求

- Node.js: 18.20.4
- Git: 支持 submodule

## 🏗 项目架构

```
VPP/
├── Fusion-template/           # 主应用模板
│   └── main/                  # 主应用
│       ├── src/
│       │   ├── api/          # API 接口
│       │   ├── config/       # 配置文件
│       │   ├── layout/       # 布局组件
│       │   ├── omega/        # Omega 框架集成
│       │   ├── plugins/      # 插件模块
│       │   ├── projects/     # 项目页面
│       │   ├── router/       # 路由配置
│       │   ├── store/        # 状态管理
│       │   └── utils/        # 工具函数
│       ├── public/           # 静态资源
│       ├── package.json      # 主应用依赖
│       └── vue.config.js     # Vue CLI 配置
├── plugins/                   # 插件目录
│   └── sub/                  # 子应用插件
│       ├── src/
│       │   ├── api/          # API 接口
│       │   ├── config/       # 配置文件
│       │   ├── hooks/        # Vue Hooks
│       │   ├── omega/        # Omega 框架集成
│       │   ├── projects/     # 项目页面
│       │   ├── router/       # 路由配置
│       │   ├── store/        # 状态管理
│       │   └── utils/        # 工具函数
│       ├── public/           # 静态资源
│       └── package.json      # 子应用依赖
├── package.json              # 根项目配置
├── pnpm-workspace.yaml       # pnpm 工作区配置
└── README.md                 # 项目文档
```

# 🚀 快速开始

## 安装所有工作区依赖
```bash
pnpm install
```

## 启动开发服务器
```bash
pnpm dev:main
```
# 或
```bash
pnpm --filter main dev
```

## 启动子应用插件
```bash
pnpm dev:sub
```
# 或
```bash
pnpm --filter sub dev
```


## 📁 模块详解

### 主应用 (Fusion-template/main)

主应用是整个平台的核心，负责：

- **用户认证与授权**: 基于 @omega/auth 的统一认证
- **布局管理**: 使用 @omega/layout 提供统一的页面布局
- **插件管理**: 动态加载和管理子应用插件
- **全局状态管理**: 跨应用的状态共享
- **主题系统**: 基于 @omega/theme 的主题切换

**核心依赖**:
- `@omega/app`: 应用框架核心
- `@omega/auth`: 认证授权模块
- `@omega/layout`: 布局组件
- `@altair/blade`: 业务组件库
- `@altair/lord`: 高级组件库
- `cet-graph`: 图形可视化组件

### 子应用插件 (plugins/sub)

子应用是独立的功能模块，特点：

- **独立开发**: 可以独立开发、测试和部署
- **功能专一**: 专注于特定业务功能
- **轻量化**: 相比主应用更加轻量，启动更快
- **可插拔**: 支持动态加载和卸载

**核心依赖**:
- `@omega/app`: 应用框架核心
- `@altair/knight`: 轻量级组件库
- `@omega/dashboard`: 仪表板组件
- `driver.js`: 用户引导组件


## 🔧 开发指南

#### 本地代理配置

在项目目录下创建 `var` 目录，新建代理配置文件：

```javascript
// var/proxy.local.js
module.exports = {
  '/api': {
    target: 'http://localhost:8080',
    changeOrigin: true,
    pathRewrite: {
      '^/api': ''
    }
  }
}
```

## 📦 构建与部署

### 构建命令

```bash
# 构建主应用
pnpm --filter main build

# 构建子应用
pnpm --filter sub build

# 构建并生成分析报告
pnpm --filter main build:report
pnpm --filter sub build:report

# DevOps 构建（主应用）
pnpm --filter main build:devops
```

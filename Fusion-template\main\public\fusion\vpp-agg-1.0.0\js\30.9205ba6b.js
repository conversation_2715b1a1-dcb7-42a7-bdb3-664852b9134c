"use strict";(self["webpackChunkvpp_agg"]=self["webpackChunkvpp_agg"]||[]).push([[30],{9724:function(e,t,i){i.r(t),i.d(t,{default:function(){return C}});var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"pageContain page2"},[t("div",{staticClass:"pageContain"},[t("el-button",{attrs:{type:"primary",id:"btn1"}},[e._v("第一步")]),t("el-button",{attrs:{type:"primary",id:"btn2"}},[e._v("第二步")]),t("el-button",{attrs:{type:"primary",id:"btn3"}},[e._v("打开弹框")])],1),e.AddFormDialog.openTrigger?t("TestDialog",e._b({ref:"TestDialog",on:{useEventHandler:e.use<PERSON>vent<PERSON>and<PERSON>}},"TestDialog",e.AddFormDialog,!1)):e._e()],1)},n=[],r=i(8635),a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"GuideDialog"},[t("CetDialog",e._g(e._b({key:e.openTrigger_in,tag:"components",scopedSlots:e._u([{key:"footer",fn:function(){return[t("span",[t("CetButton",e._g(e._b({},"CetButton",e.CetButton_cancel,!1),e.CetButton_cancel.event))],1)]},proxy:!0}])},"components",e.CetDialog_pagedialog,!1),e.CetDialog_pagedialog.event),[t("el-container")],1)],1)},l=[],s={name:"GuideDialog",components:{},props:{openTrigger_in:{type:Number},closeTrigger_in:{type:Number}},computed:{},watch:{},data(){return{CetDialog_pagedialog:{openTrigger_in:(new Date).getTime(),closeTrigger_in:(new Date).getTime(),title:$T("新手引导"),width:"900px",event:{openTrigger_out:this.CetDialog_pagedialog_openTrigger_out,closeTrigger_out:this.CetDialog_pagedialog_closeTrigger_out}},CetDrawer:{isVisible:!1,direction:"rtl",size:"60%"},CetButton_cancel:{visible_in:!0,disable_in:!1,title:$T("取消"),type:"",plain:!0,event:{statusTrigger_out:this.CetButton_cancel_statusTrigger_out}},selectNode:{},CetTree:{inputData_in:[],selectNode:{},checkedNodes:[],filterNodes_in:null,searchText_in:"",showFilter:!0,ShowRootNode:!1,nodeKey:"tree_id",props:{label:"name",children:"children"},highlightCurrent:!0,showCheckbox:!1,checkStrictly:!1,defaultExpandAll:!0,event:{currentNode_out:this.CetTree_currentNode_out}}}},methods:{onGuideClick(e){this.$emit("useEventHandler","onGuideClick",e)},CetButton_cancel_statusTrigger_out(){this.$emit("useEventHandler","onClose")},CetDialog_pagedialog_openTrigger_out(e){this.$emit("openTrigger_out",e)},CetDialog_pagedialog_closeTrigger_out(e){this.$emit("closeTrigger_out",e)},CetTree_currentNode_out(e){this.selectNode=e}},created(){},mounted(){}},g=s,c=i(3236),u=(0,c.A)(g,a,l,!1,null,"a72dca40",null),p=u.exports,d=i(2462);const[_]=(0,d.A)();var T={name:"noviceGuide-page1",components:{TestDialog:p},data(){return{AddFormDialog:{openTrigger:!1,openTrigger_in:(new Date).getTime(),closeTrigger_in:(new Date).getTime(),queryId_in:0,inputData_in:null}}},mounted(){this.initDriver()},methods:{initDriver(){const{step:e}=r.F.getRouterQuery();e&&(_.setSteps([{element:"#btn1",popover:{title:$T("第一步"),description:"Description"}},{element:"#btn2",popover:{title:$T("第二步"),description:"Description"}},{element:"#btn3",popover:{title:$T("打开弹框"),description:"点击完成,打开一个弹框",onNextClick:()=>{this.onOpenClick(),_.moveNext()},onPrevClick:()=>{_.movePrevious()}}}]),_.drive())},onOpenClick(){this.AddFormDialog.openTrigger=!0,this.AddFormDialog.openTrigger_in=(new Date).getTime(),this.$nextTick(()=>{this.$refs.TestDialog.CetDialog_pagedialog.openTrigger_in=(new Date).getTime()})},onClose(){this.isDrawerMode=!1,this.AddFormDialog.openTrigger=!1},useEventHandler(e,...t){if("string"!==typeof e||!this.hasOwnProperty(e))return void console.warn(`Invalid function name: ${e}`);const i=this[e];if("function"===typeof i)try{i.call(this,...t)}catch(o){console.error(`Error executing function ${e}:`,o)}else console.warn(` ${e} is not a function`)}}},h=T,m=(0,c.A)(h,o,n,!1,null,"e5f6a268",null),C=m.exports}}]);
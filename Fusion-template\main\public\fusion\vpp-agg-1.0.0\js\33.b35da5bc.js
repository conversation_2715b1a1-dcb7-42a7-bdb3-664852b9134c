"use strict";(self["webpackChunkvpp_agg"]=self["webpackChunkvpp_agg"]||[]).push([[33],{8033:function(t,l,e){e.r(l),e.d(l,{default:function(){return d}});var a=function(){var t=this,l=t._self._c;return l("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData,border:""}},[l("el-table-column",{attrs:{fixed:"",prop:"date",label:"日期",width:"150"}}),l("el-table-column",{attrs:{prop:"name",label:"姓名",width:"120"}}),l("el-table-column",{attrs:{prop:"province",label:"省份",width:"120"}}),l("el-table-column",{attrs:{prop:"city",label:"市区",width:"120"}}),l("el-table-column",{attrs:{prop:"address",label:"地址",width:"300"}}),l("el-table-column",{attrs:{prop:"zip",label:"邮编",width:"120"}}),l("el-table-column",{attrs:{fixed:"right",label:"操作",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[l("el-button",{attrs:{type:"text",size:"small"},on:{click:function(l){return t.handleClick(e.row)}}},[t._v(" 查看 ")]),l("el-button",{attrs:{type:"text",size:"small"},on:{click:function(l){return t.handleClick(l)}}},[t._v(" 编辑 "),l("OmegaIcon",{attrs:{symbolId:"collect-lin",size:"middle"},on:{click:t.evCollectClick}})],1)]}}])})],1)},n=[],o=(e(3580),e(3062)),r=e.n(o),c={name:"gridDemo",components:{OmegaIcon:r()},methods:{handleClick(t){},evCollectClick(){console.log("xxxxxxxxxxxx")}},data(){let t=[];for(let l=0;l<50;l++)t.push({date:"2016-05-02",name:"王小虎",province:"上海",city:"普陀区",address:"上海市普陀区金沙江路 1518 弄",zip:l});return{tableData:t}}},i=c,s=e(3236),u=(0,s.A)(i,a,n,!1,null,null,null),d=u.exports}}]);
__registeAppConfig.setAppconfig({
  version: "1.0.0",
  i18n: {
    en: {
      示例: "example",
      表格: "grid"
    }
  },
  navmenu: [
    {
      label: "示例",
      icon: "permission-management-lin",
      type: "subMenu",
      subMenuList: [
        {
          label: "表格",
          category: "project",
          type: "menuItem",
          location: "/grid",
          permission: "grid"
        },
        {
          label: "表格",
          category: "platform",
          type: "menuItem",
          location: "/grid",
          permission: "grid"
        },
        {
          label: "引导页面1",
          category: "platform",
          type: "menuItem",
          location: "/noviceGuidePage1",
          permission: "project-in"
        },
        {
          label: "引导页面2",
          category: "platform",
          type: "menuItem",
          location: "/noviceGuidePage2",
          permission: "project-in"
        }
      ]
    }
  ],
  newGuideSteps: [
    {
      name: "新手引导",
      icon: "",
      desc: "",
      children: [
        {
          step: 1,
          path: "/noviceGuidePage1",
          name: "引导页面1",
          icon: "",
          desc: ""
        },
        {
          step: 2,
          path: "/noviceGuidePage2",
          name: "引导页面2",
          icon: "",
          desc: ""
        }
      ]
    },
    {
      name: "权限管理",
      icon: "",
      desc: "",
      children: [
        {
          step: 1,
          path: "/usermanage",
          name: "平台用户管理",
          icon: "",
          desc: "",
          children: [
            {
              step: 2,
              path: "/usermanage",
              name: "添加用户组",
              icon: "",
              desc: ""
            },
            {
              step: 3,
              path: "/usermanage",
              name: "添加用户",
              icon: "",
              desc: ""
            }
          ]
        }
      ]
    }
  ]
});

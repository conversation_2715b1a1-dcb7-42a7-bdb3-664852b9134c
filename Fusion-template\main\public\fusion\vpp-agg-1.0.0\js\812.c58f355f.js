"use strict";(self["webpackChunkvpp_agg"]=self["webpackChunkvpp_agg"]||[]).push([[812],{2462:function(e,t,o){o.d(t,{A:function(){return ee}});let n,i={};function r(e={}){i={animate:!0,allowClose:!0,overlayClickBehavior:"close",overlayOpacity:.7,smoothScroll:!1,disableActiveInteraction:!1,showProgress:!1,stagePadding:10,stageRadius:5,popoverOffset:10,showButtons:["next","previous","close"],disableButtons:[],overlayColor:"#000",...e}}function s(e){return e?i[e]:i}function l(e){n=e}function d(){return n}let a={};function p(e,t){a[e]=t}function c(e){var t;null==(t=a[e])||t.call(a)}function v(){a={}}function u(e,t,o,n){return(e/=n/2)<1?o/2*e*e+t:-o/2*(--e*(e-2)-1)+t}function h(e){const t='a[href]:not([disabled]), button:not([disabled]), textarea:not([disabled]), input[type="text"]:not([disabled]), input[type="radio"]:not([disabled]), input[type="checkbox"]:not([disabled]), select:not([disabled])';return e.flatMap(e=>{const o=e.matches(t),n=Array.from(e.querySelectorAll(t));return[...o?[e]:[],...n]}).filter(e=>"none"!==getComputedStyle(e).pointerEvents&&f(e))}function m(e){if(!e||g(e))return;const t=s("smoothScroll"),o=e.offsetHeight>window.innerHeight;e.scrollIntoView({behavior:!t||w(e)?"auto":"smooth",inline:"center",block:o?"start":"center"})}function w(e){if(!e||!e.parentElement)return;const t=e.parentElement;return t.scrollHeight>t.clientHeight}function g(e){const t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&t.right<=(window.innerWidth||document.documentElement.clientWidth)}function f(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)}let y={};function x(e,t){y[e]=t}function b(e){return e?y[e]:y}function C(){y={}}function _(e,t,o,n){let i=b("__activeStagePosition");const r=i||o.getBoundingClientRect(),s=n.getBoundingClientRect(),l=u(e,r.x,s.x-r.x,t),d=u(e,r.y,s.y-r.y,t),a=u(e,r.width,s.width-r.width,t),p=u(e,r.height,s.height-r.height,t);i={x:l,y:d,width:a,height:p},$(i),x("__activeStagePosition",i)}function k(e){if(!e)return;const t=e.getBoundingClientRect(),o={x:t.x,y:t.y,width:t.width,height:t.height};x("__activeStagePosition",o),$(o)}function E(){const e=b("__activeStagePosition"),t=b("__overlaySvg");if(!e)return;if(!t)return void console.warn("No stage svg found.");const o=window.innerWidth,n=window.innerHeight;t.setAttribute("viewBox",`0 0 ${o} ${n}`)}function L(e){const t=B(e);document.body.appendChild(t),R(t,e=>{"path"===e.target.tagName&&c("overlayClick")}),x("__overlaySvg",t)}function $(e){const t=b("__overlaySvg");if(!t)return void L(e);const o=t.firstElementChild;if("path"!==(null==o?void 0:o.tagName))throw new Error("no path element found in stage svg");o.setAttribute("d",P(e))}function B(e){const t=window.innerWidth,o=window.innerHeight,n=document.createElementNS("http://www.w3.org/2000/svg","svg");n.classList.add("driver-overlay","driver-overlay-animated"),n.setAttribute("viewBox",`0 0 ${t} ${o}`),n.setAttribute("xmlSpace","preserve"),n.setAttribute("xmlnsXlink","http://www.w3.org/1999/xlink"),n.setAttribute("version","1.1"),n.setAttribute("preserveAspectRatio","xMinYMin slice"),n.style.fillRule="evenodd",n.style.clipRule="evenodd",n.style.strokeLinejoin="round",n.style.strokeMiterlimit="2",n.style.zIndex="10000",n.style.position="fixed",n.style.top="0",n.style.left="0",n.style.width="100%",n.style.height="100%";const i=document.createElementNS("http://www.w3.org/2000/svg","path");return i.setAttribute("d",P(e)),i.style.fill=s("overlayColor")||"rgb(0,0,0)",i.style.opacity=`${s("overlayOpacity")}`,i.style.pointerEvents="auto",i.style.cursor="auto",n.appendChild(i),n}function P(e){const t=window.innerWidth,o=window.innerHeight,n=s("stagePadding")||0,i=s("stageRadius")||0,r=e.width+2*n,l=e.height+2*n,d=Math.min(i,r/2,l/2),a=Math.floor(Math.max(d,0)),p=e.x-n+a,c=e.y-n,v=r-2*a,u=l-2*a;return`M${t},0L0,0L0,${o}L${t},${o}L${t},0Z\n    M${p},${c} h${v} a${a},${a} 0 0 1 ${a},${a} v${u} a${a},${a} 0 0 1 -${a},${a} h-${v} a${a},${a} 0 0 1 -${a},-${a} v-${u} a${a},${a} 0 0 1 ${a},-${a} z`}function A(){const e=b("__overlaySvg");e&&e.remove()}function H(){const e=document.getElementById("driver-dummy-element");if(e)return e;let t=document.createElement("div");return t.id="driver-dummy-element",t.style.width="0",t.style.height="0",t.style.pointerEvents="none",t.style.opacity="0",t.style.position="fixed",t.style.top="50%",t.style.left="50%",document.body.appendChild(t),t}function S(e){const{element:t}=e;let o="function"==typeof t?t():"string"==typeof t?document.querySelector(t):t;o||(o=H()),M(o,e)}function T(){const e=b("__activeElement"),t=b("__activeStep");e&&(k(e),E(),V(e,t))}function M(e,t){var o;const n=Date.now(),i=b("__activeStep"),r=b("__activeElement")||e,l=!r||r===e,a="driver-dummy-element"===e.id,p="driver-dummy-element"===r.id,c=s("animate"),v=t.onHighlightStarted||s("onHighlightStarted"),u=(null==t?void 0:t.onHighlighted)||s("onHighlighted"),h=(null==i?void 0:i.onDeselected)||s("onDeselected"),w=s(),g=b();!l&&h&&h(p?void 0:r,i,{config:w,state:g,driver:d()}),v&&v(a?void 0:e,t,{config:w,state:g,driver:d()});const f=!l&&c;let y=!1;q(),x("previousStep",i),x("previousElement",r),x("activeStep",t),x("activeElement",e);const C=()=>{if(b("__transitionCallback")!==C)return;const o=Date.now()-n,l=400-o<=200;t.popover&&l&&!y&&f&&(F(e,t),y=!0),s("animate")&&o<400?_(o,400,r,e):(k(e),u&&u(a?void 0:e,t,{config:s(),state:b(),driver:d()}),x("__transitionCallback",void 0),x("__previousStep",i),x("__previousElement",r),x("__activeStep",t),x("__activeElement",e)),window.requestAnimationFrame(C)};x("__transitionCallback",C),window.requestAnimationFrame(C),m(e),!f&&t.popover&&F(e,t),r.classList.remove("driver-active-element","driver-no-interaction"),r.removeAttribute("aria-haspopup"),r.removeAttribute("aria-expanded"),r.removeAttribute("aria-controls"),(null!=(o=t.disableActiveInteraction)?o:s("disableActiveInteraction"))&&e.classList.add("driver-no-interaction"),e.classList.add("driver-active-element"),e.setAttribute("aria-haspopup","dialog"),e.setAttribute("aria-expanded","true"),e.setAttribute("aria-controls","driver-popover-content")}function D(){var e;null==(e=document.getElementById("driver-dummy-element"))||e.remove(),document.querySelectorAll(".driver-active-element").forEach(e=>{e.classList.remove("driver-active-element","driver-no-interaction"),e.removeAttribute("aria-haspopup"),e.removeAttribute("aria-expanded"),e.removeAttribute("aria-controls")})}function W(){const e=b("__resizeTimeout");e&&window.cancelAnimationFrame(e),x("__resizeTimeout",window.requestAnimationFrame(T))}function I(e){var t;if(!b("isInitialized")||"Tab"!==e.key&&9!==e.keyCode)return;const o=b("__activeElement"),n=null==(t=b("popover"))?void 0:t.wrapper,i=h([...n?[n]:[],...o?[o]:[]]),r=i[0],s=i[i.length-1];if(e.preventDefault(),e.shiftKey){const e=i[i.indexOf(document.activeElement)-1]||s;null==e||e.focus()}else{const e=i[i.indexOf(document.activeElement)+1]||r;null==e||e.focus()}}function N(e){var t;(null==(t=s("allowKeyboardControl"))||t)&&("Escape"===e.key?c("escapePress"):"ArrowRight"===e.key?c("arrowRightPress"):"ArrowLeft"===e.key&&c("arrowLeftPress"))}function R(e,t,o){const n=(t,n)=>{const i=t.target;e.contains(i)&&((!o||o(i))&&(t.preventDefault(),t.stopPropagation(),t.stopImmediatePropagation()),null==n||n(t))};document.addEventListener("pointerdown",n,!0),document.addEventListener("mousedown",n,!0),document.addEventListener("pointerup",n,!0),document.addEventListener("mouseup",n,!0),document.addEventListener("click",e=>{n(e,t)},!0)}function z(){window.addEventListener("keyup",N,!1),window.addEventListener("keydown",I,!1),window.addEventListener("resize",W),window.addEventListener("scroll",W)}function O(){window.removeEventListener("keyup",N),window.removeEventListener("resize",W),window.removeEventListener("scroll",W)}function q(){const e=b("popover");e&&(e.wrapper.style.display="none")}function F(e,t){var o,n;let i=b("popover");i&&document.body.removeChild(i.wrapper),i=Z(),document.body.appendChild(i.wrapper);const{title:r,description:l,showButtons:a,disableButtons:p,showProgress:v,nextBtnText:u=s("nextBtnText")||"Next &rarr;",prevBtnText:w=s("prevBtnText")||"&larr; Previous",progressText:g=s("progressText")||"{current} of {total}"}=t.popover||{};i.nextButton.innerHTML=u,i.previousButton.innerHTML=w,i.progress.innerHTML=g,r?(i.title.innerHTML=r,i.title.style.display="block"):i.title.style.display="none",l?(i.description.innerHTML=l,i.description.style.display="block"):i.description.style.display="none";const f=a||s("showButtons"),y=v||s("showProgress")||!1,C=(null==f?void 0:f.includes("next"))||(null==f?void 0:f.includes("previous"))||y;i.closeButton.style.display=f.includes("close")?"block":"none",C?(i.footer.style.display="flex",i.progress.style.display=y?"block":"none",i.nextButton.style.display=f.includes("next")?"block":"none",i.previousButton.style.display=f.includes("previous")?"block":"none"):i.footer.style.display="none";const _=p||s("disableButtons")||[];null!=_&&_.includes("next")&&(i.nextButton.disabled=!0,i.nextButton.classList.add("driver-popover-btn-disabled")),null!=_&&_.includes("previous")&&(i.previousButton.disabled=!0,i.previousButton.classList.add("driver-popover-btn-disabled")),null!=_&&_.includes("close")&&(i.closeButton.disabled=!0,i.closeButton.classList.add("driver-popover-btn-disabled"));const k=i.wrapper;k.style.display="block",k.style.left="",k.style.top="",k.style.bottom="",k.style.right="",k.id="driver-popover-content",k.setAttribute("role","dialog"),k.setAttribute("aria-labelledby","driver-popover-title"),k.setAttribute("aria-describedby","driver-popover-description");const E=i.arrow;E.className="driver-popover-arrow";const L=(null==(o=t.popover)?void 0:o.popoverClass)||s("popoverClass")||"";k.className=`driver-popover ${L}`.trim(),R(i.wrapper,o=>{var n,i,r;const l=o.target,a=(null==(n=t.popover)?void 0:n.onNextClick)||s("onNextClick"),p=(null==(i=t.popover)?void 0:i.onPrevClick)||s("onPrevClick"),v=(null==(r=t.popover)?void 0:r.onCloseClick)||s("onCloseClick");return l.closest(".driver-popover-next-btn")?a?a(e,t,{config:s(),state:b(),driver:d()}):c("nextClick"):l.closest(".driver-popover-prev-btn")?p?p(e,t,{config:s(),state:b(),driver:d()}):c("prevClick"):l.closest(".driver-popover-close-btn")?v?v(e,t,{config:s(),state:b(),driver:d()}):c("closeClick"):void 0},e=>!(null!=i&&i.description.contains(e))&&!(null!=i&&i.title.contains(e))&&"string"==typeof e.className&&e.className.includes("driver-popover")),x("popover",i);const $=(null==(n=t.popover)?void 0:n.onPopoverRender)||s("onPopoverRender");$&&$(i,{config:s(),state:b(),driver:d()}),V(e,t),m(k);const B=e.classList.contains("driver-dummy-element"),P=h([k,...B?[]:[e]]);P.length>0&&P[0].focus()}function Y(){const e=b("popover");if(null==e||!e.wrapper)return;const t=e.wrapper.getBoundingClientRect(),o=s("stagePadding")||0,n=s("popoverOffset")||0;return{width:t.width+o+n,height:t.height+o+n,realWidth:t.width,realHeight:t.height}}function K(e,t){const{elementDimensions:o,popoverDimensions:n,popoverPadding:i,popoverArrowDimensions:r}=t;return"start"===e?Math.max(Math.min(o.top-i,window.innerHeight-n.realHeight-r.width),r.width):"end"===e?Math.max(Math.min(o.top-(null==n?void 0:n.realHeight)+o.height+i,window.innerHeight-(null==n?void 0:n.realHeight)-r.width),r.width):"center"===e?Math.max(Math.min(o.top+o.height/2-(null==n?void 0:n.realHeight)/2,window.innerHeight-(null==n?void 0:n.realHeight)-r.width),r.width):0}function j(e,t){const{elementDimensions:o,popoverDimensions:n,popoverPadding:i,popoverArrowDimensions:r}=t;return"start"===e?Math.max(Math.min(o.left-i,window.innerWidth-n.realWidth-r.width),r.width):"end"===e?Math.max(Math.min(o.left-(null==n?void 0:n.realWidth)+o.width+i,window.innerWidth-(null==n?void 0:n.realWidth)-r.width),r.width):"center"===e?Math.max(Math.min(o.left+o.width/2-(null==n?void 0:n.realWidth)/2,window.innerWidth-(null==n?void 0:n.realWidth)-r.width),r.width):0}function V(e,t){const o=b("popover");if(!o)return;const{align:n="start",side:i="left"}=(null==t?void 0:t.popover)||{},r=n,l="driver-dummy-element"===e.id?"over":i,d=s("stagePadding")||0,a=Y(),p=o.arrow.getBoundingClientRect(),c=e.getBoundingClientRect(),v=c.top-a.height;let u=v>=0;const h=window.innerHeight-(c.bottom+a.height);let m=h>=0;const w=c.left-a.width;let g=w>=0;const f=window.innerWidth-(c.right+a.width);let y=f>=0;const x=!u&&!m&&!g&&!y;let C=l;if("top"===l&&u?y=g=m=!1:"bottom"===l&&m?y=g=u=!1:"left"===l&&g?y=u=m=!1:"right"===l&&y&&(g=u=m=!1),"over"===l){const e=window.innerWidth/2-a.realWidth/2,t=window.innerHeight/2-a.realHeight/2;o.wrapper.style.left=`${e}px`,o.wrapper.style.right="auto",o.wrapper.style.top=`${t}px`,o.wrapper.style.bottom="auto"}else if(x){const e=window.innerWidth/2-(null==a?void 0:a.realWidth)/2,t=10;o.wrapper.style.left=`${e}px`,o.wrapper.style.right="auto",o.wrapper.style.bottom=`${t}px`,o.wrapper.style.top="auto"}else if(g){const e=Math.min(w,window.innerWidth-(null==a?void 0:a.realWidth)-p.width),t=K(r,{elementDimensions:c,popoverDimensions:a,popoverPadding:d,popoverArrowDimensions:p});o.wrapper.style.left=`${e}px`,o.wrapper.style.top=`${t}px`,o.wrapper.style.bottom="auto",o.wrapper.style.right="auto",C="left"}else if(y){const e=Math.min(f,window.innerWidth-(null==a?void 0:a.realWidth)-p.width),t=K(r,{elementDimensions:c,popoverDimensions:a,popoverPadding:d,popoverArrowDimensions:p});o.wrapper.style.right=`${e}px`,o.wrapper.style.top=`${t}px`,o.wrapper.style.bottom="auto",o.wrapper.style.left="auto",C="right"}else if(u){const e=Math.min(v,window.innerHeight-a.realHeight-p.width);let t=j(r,{elementDimensions:c,popoverDimensions:a,popoverPadding:d,popoverArrowDimensions:p});o.wrapper.style.top=`${e}px`,o.wrapper.style.left=`${t}px`,o.wrapper.style.bottom="auto",o.wrapper.style.right="auto",C="top"}else if(m){const e=Math.min(h,window.innerHeight-(null==a?void 0:a.realHeight)-p.width);let t=j(r,{elementDimensions:c,popoverDimensions:a,popoverPadding:d,popoverArrowDimensions:p});o.wrapper.style.left=`${t}px`,o.wrapper.style.bottom=`${e}px`,o.wrapper.style.top="auto",o.wrapper.style.right="auto",C="bottom"}x?o.arrow.classList.add("driver-popover-arrow-none"):X(r,C,e)}function X(e,t,o){const n=b("popover");if(!n)return;const i=o.getBoundingClientRect(),r=Y(),l=n.arrow,d=r.width,a=window.innerWidth,p=i.width,c=i.left,v=r.height,u=window.innerHeight,h=i.top,m=i.height;l.className="driver-popover-arrow";let w=t,g=e;if("top"===t?(c+p<=0?(w="right",g="end"):c+p-d<=0&&(w="top",g="start"),c>=a?(w="left",g="end"):c+d>=a&&(w="top",g="end")):"bottom"===t?(c+p<=0?(w="right",g="start"):c+p-d<=0&&(w="bottom",g="start"),c>=a?(w="left",g="start"):c+d>=a&&(w="bottom",g="end")):"left"===t?(h+m<=0?(w="bottom",g="end"):h+m-v<=0&&(w="left",g="start"),h>=u?(w="top",g="end"):h+v>=u&&(w="left",g="end")):"right"===t&&(h+m<=0?(w="bottom",g="start"):h+m-v<=0&&(w="right",g="start"),h>=u?(w="top",g="start"):h+v>=u&&(w="right",g="end")),w){l.classList.add(`driver-popover-arrow-side-${w}`),l.classList.add(`driver-popover-arrow-align-${g}`);const e=o.getBoundingClientRect(),i=l.getBoundingClientRect(),r=s("stagePadding")||0,d=e.left-r<window.innerWidth&&e.right+r>0&&e.top-r<window.innerHeight&&e.bottom+r>0;"bottom"===t&&d&&(i.x>e.x&&i.x+i.width<e.x+e.width?n.wrapper.style.transform="translateY(0)":(l.classList.remove(`driver-popover-arrow-align-${g}`),l.classList.add("driver-popover-arrow-none"),n.wrapper.style.transform=`translateY(-${r/2}px)`))}else l.classList.add("driver-popover-arrow-none")}function Z(){const e=document.createElement("div");e.classList.add("driver-popover");const t=document.createElement("div");t.classList.add("driver-popover-arrow");const o=document.createElement("header");o.id="driver-popover-title",o.classList.add("driver-popover-title"),o.style.display="none",o.innerText="Popover Title";const n=document.createElement("div");n.id="driver-popover-description",n.classList.add("driver-popover-description"),n.style.display="none",n.innerText="Popover description is here";const i=document.createElement("button");i.type="button",i.classList.add("driver-popover-close-btn"),i.setAttribute("aria-label","Close"),i.innerHTML="&times;";const r=document.createElement("footer");r.classList.add("driver-popover-footer");const s=document.createElement("span");s.classList.add("driver-popover-progress-text"),s.innerText="";const l=document.createElement("span");l.classList.add("driver-popover-navigation-btns");const d=document.createElement("button");d.type="button",d.classList.add("driver-popover-prev-btn"),d.innerHTML="&larr; Previous";const a=document.createElement("button");return a.type="button",a.classList.add("driver-popover-next-btn"),a.innerHTML="Next &rarr;",l.appendChild(d),l.appendChild(a),r.appendChild(s),r.appendChild(l),e.appendChild(i),e.appendChild(t),e.appendChild(o),e.appendChild(n),e.appendChild(r),{wrapper:e,arrow:t,title:o,description:n,footer:r,previousButton:d,nextButton:a,closeButton:i,footerButtons:l,progress:s}}function G(){var e;const t=b("popover");t&&(null==(e=t.wrapper.parentElement)||e.removeChild(t.wrapper))}function J(e={}){function t(){s("allowClose")&&w()}function o(){const e=s("overlayClickBehavior");s("allowClose")&&"close"===e?w():"nextStep"===e&&n()}function n(){const e=b("activeIndex"),t=s("steps")||[];if("undefined"==typeof e)return;const o=e+1;t[o]?m(o):w()}function i(){const e=b("activeIndex"),t=s("steps")||[];if("undefined"==typeof e)return;const o=e-1;t[o]?m(o):w()}function a(e){(s("steps")||[])[e]?m(e):w()}function c(){var e;if(b("__transitionCallback"))return;const t=b("activeIndex"),o=b("__activeStep"),n=b("__activeElement");if("undefined"==typeof t||"undefined"==typeof o||"undefined"==typeof b("activeIndex"))return;const r=(null==(e=o.popover)?void 0:e.onPrevClick)||s("onPrevClick");if(r)return r(n,o,{config:s(),state:b(),driver:d()});i()}function u(){var e;if(b("__transitionCallback"))return;const t=b("activeIndex"),o=b("__activeStep"),i=b("__activeElement");if("undefined"==typeof t||"undefined"==typeof o)return;const r=(null==(e=o.popover)?void 0:e.onNextClick)||s("onNextClick");if(r)return r(i,o,{config:s(),state:b(),driver:d()});n()}function h(){b("isInitialized")||(x("isInitialized",!0),document.body.classList.add("driver-active",s("animate")?"driver-fade":"driver-simple"),z(),p("overlayClick",o),p("escapePress",t),p("arrowLeftPress",c),p("arrowRightPress",u))}function m(e=0){var t,o,n,i,r,l,d,a;const p=s("steps");if(!p)return console.error("No steps to drive through"),void w();if(!p[e])return void w();x("__activeOnDestroyed",document.activeElement),x("activeIndex",e);const c=p[e],v=p[e+1],u=p[e-1],h=(null==(t=c.popover)?void 0:t.doneBtnText)||s("doneBtnText")||"Done",g=s("allowClose"),f="undefined"!=typeof(null==(o=c.popover)?void 0:o.showProgress)?null==(n=c.popover)?void 0:n.showProgress:s("showProgress"),y=((null==(i=c.popover)?void 0:i.progressText)||s("progressText")||"{{current}} of {{total}}").replace("{{current}}",`${e+1}`).replace("{{total}}",`${p.length}`),b=(null==(r=c.popover)?void 0:r.showButtons)||s("showButtons"),C=["next","previous",...g?["close"]:[]].filter(e=>!(null!=b&&b.length)||b.includes(e)),_=(null==(l=c.popover)?void 0:l.onNextClick)||s("onNextClick"),k=(null==(d=c.popover)?void 0:d.onPrevClick)||s("onPrevClick"),E=(null==(a=c.popover)?void 0:a.onCloseClick)||s("onCloseClick");S({...c,popover:{showButtons:C,nextBtnText:v?void 0:h,disableButtons:[...u?[]:["previous"]],showProgress:f,progressText:y,onNextClick:_||(()=>{v?m(e+1):w()}),onPrevClick:k||(()=>{m(e-1)}),onCloseClick:E||(()=>{w()}),...(null==c?void 0:c.popover)||{}}})}function w(e=!0){const t=b("__activeElement"),o=b("__activeStep"),n=b("__activeOnDestroyed"),i=s("onDestroyStarted");if(e&&i){const e=!t||"driver-dummy-element"===(null==t?void 0:t.id);return void i(e?void 0:t,o,{config:s(),state:b(),driver:d()})}const r=(null==o?void 0:o.onDeselected)||s("onDeselected"),l=s("onDestroyed");if(document.body.classList.remove("driver-active","driver-fade","driver-simple"),O(),G(),D(),A(),v(),C(),t&&o){const e="driver-dummy-element"===t.id;r&&r(e?void 0:t,o,{config:s(),state:b(),driver:d()}),l&&l(e?void 0:t,o,{config:s(),state:b(),driver:d()})}n&&n.focus()}r(e);const g={isActive:()=>b("isInitialized")||!1,refresh:W,drive:(e=0)=>{h(),m(e)},setConfig:r,setSteps:e=>{C(),r({...s(),steps:e})},getConfig:s,getState:b,getActiveIndex:()=>b("activeIndex"),isFirstStep:()=>0===b("activeIndex"),isLastStep:()=>{const e=s("steps")||[],t=b("activeIndex");return void 0!==t&&t===e.length-1},getActiveStep:()=>b("activeStep"),getActiveElement:()=>b("activeElement"),getPreviousElement:()=>b("previousElement"),getPreviousStep:()=>b("previousStep"),moveNext:n,movePrevious:i,moveTo:a,hasNextStep:()=>{const e=s("steps")||[],t=b("activeIndex");return void 0!==t&&!!e[t+1]},hasPreviousStep:()=>{const e=s("steps")||[],t=b("activeIndex");return void 0!==t&&!!e[t-1]},highlight:e=>{h(),S({...e,popover:e.popover?{showButtons:[],showProgress:!1,progressText:"",...e.popover}:void 0})},destroy:()=>{w(!1)}};return l(g),g}const Q={showProgress:!0,allowClose:!1,animate:!0,opacity:.75,padding:5,showButtons:!0,prevBtnText:$T("上一步"),nextBtnText:$T("下一步"),doneBtnText:$T("完成"),progressText:"{{current}} / {{total}}"},U=(e={})=>{const t=J();return t.setConfig({...e,...Q}),[t]};var ee=U},8635:function(e,t,o){o.d(t,{F:function(){return i}});var n=o(2670);const i=n.v}}]);
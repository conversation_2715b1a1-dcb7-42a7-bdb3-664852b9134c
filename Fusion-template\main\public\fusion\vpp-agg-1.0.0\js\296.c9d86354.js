"use strict";(self["webpackChunkvpp_agg"]=self["webpackChunkvpp_agg"]||[]).push([[296],{5296:function(t,e,a){a.r(e),a.d(e,{default:function(){return c}});var l=function(){var t=this,e=t._self._c;return e("div",{staticClass:"list-container"},[e("div",{staticClass:"search-section"},[e("div",{staticClass:"search-form"},[e("el-form",{staticClass:"search-form-inline",attrs:{inline:!0,model:t.searchForm}},[e("el-form-item",[e("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入关键字",clearable:""},model:{value:t.searchForm.keyword,callback:function(e){t.$set(t.searchForm,"keyword",e)},expression:"searchForm.keyword"}},[e("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:t.handleSearch},slot:"append"})],1)],1),e("el-form-item",[e("el-select",{staticStyle:{width:"120px"},attrs:{placeholder:"全部",clearable:""},model:{value:t.searchForm.category,callback:function(e){t.$set(t.searchForm,"category",e)},expression:"searchForm.category"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"类型1",value:"type1"}}),e("el-option",{attrs:{label:"类型2",value:"type2"}})],1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("搜索")])],1)],1)],1)]),e("div",{staticClass:"table-section"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,border:"",size:"small"}},[e("el-table-column",{attrs:{prop:"index",label:"序号",width:"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1+(t.currentPage-1)*t.pageSize)+" ")]}}])}),e("el-table-column",{attrs:{prop:"computerGroup",label:"计算机机组","min-width":"150",align:"center"}}),e("el-table-column",{attrs:{prop:"unitPosition",label:"机组机位","min-width":"120",align:"center"}}),e("el-table-column",{attrs:{prop:"dataSourceCount",label:"数据源数量",width:"120",align:"center"}}),e("el-table-column",{attrs:{label:"操作",width:"180",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{staticClass:"action-btn detail-btn",attrs:{type:"text",size:"small"},on:{click:function(e){return t.handleDetail(a.row)}}},[t._v(" 详情 ")]),e("el-button",{staticClass:"action-btn edit-btn",attrs:{type:"text",size:"small"},on:{click:function(e){return t.handleEdit(a.row)}}},[t._v(" 编辑 ")]),e("el-button",{staticClass:"action-btn delete-btn",attrs:{type:"text",size:"small"},on:{click:function(e){return t.handleDelete(a.row)}}},[t._v(" 删除 ")])]}}])})],1)],1),e("div",{staticClass:"pagination-section"},[e("el-pagination",{attrs:{"current-page":t.currentPage,"page-sizes":[10,20,50,100],"page-size":t.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)])},n=[],i=(a(3580),{name:"ListPage",data(){return{loading:!1,searchForm:{keyword:"",category:""},tableData:[],currentPage:1,pageSize:10,total:0}},mounted(){this.loadData()},methods:{loadData(){this.loading=!0,setTimeout(()=>{const t=[];for(let e=1;e<=50;e++)t.push({id:e,computerGroup:`计算机机组${String(e).padStart(2,"0")}`,unitPosition:`机组机位${e}`,dataSourceCount:Math.floor(10*Math.random()),status:Math.random()>.5?"正常":"异常"});this.tableData=t.slice((this.currentPage-1)*this.pageSize,this.currentPage*this.pageSize),this.total=t.length,this.loading=!1},500)},handleSearch(){this.currentPage=1,this.loadData()},handleDetail(t){this.$message.info(`查看详情: ${t.computerGroup}`)},handleEdit(t){this.$message.info(`编辑: ${t.computerGroup}`)},handleDelete(t){this.$confirm(`确定要删除 ${t.computerGroup} 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.$message.success("删除成功"),this.loadData()}).catch(()=>{this.$message.info("已取消删除")})},handleSizeChange(t){this.pageSize=t,this.currentPage=1,this.loadData()},handleCurrentChange(t){this.currentPage=t,this.loadData()}}}),r=i,s=a(3236),o=(0,s.A)(r,l,n,!1,null,"0d5c1bfc",null),c=o.exports}}]);
"use strict";(self["webpackChunkvpp_agg"]=self["webpackChunkvpp_agg"]||[]).push([[905],{4387:function(e,t,o){o.r(t),o.d(t,{default:function(){return a}});var n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"pageContain page1"},[t("el-button",{attrs:{type:"primary",id:"btn1"}},[e._v("第一步")]),t("el-button",{attrs:{type:"primary",id:"btn2"}},[e._v("第二步")]),t("el-button",{attrs:{type:"primary",id:"btn3"},on:{click:e.onPushClick}},[e._v(" 去引导页面2 ")])],1)},i=[],r=o(8635),s=o(2462);const[l]=(0,s.A)();var c={name:"noviceGuide-page1",data(){return{}},mounted(){this.initDriver()},methods:{initDriver(){const{step:e}=r.F.getRouterQuery();e&&(l.setSteps([{element:"#btn1",popover:{title:$T("第一步"),description:"Description",onNextClick:()=>{console.log("next1"),l.moveNext()},onPrevClick:()=>{console.log("prev"),l.movePrevious()}}},{element:"#btn2",popover:{title:"第二步",description:"Description",onNextClick:()=>{console.log("next2"),l.moveNext()},onPrevClick:()=>{console.log("prev"),l.movePrevious()}}},{element:"#btn3",popover:{title:"去引导页面2",description:"Description",onNextClick:()=>{console.log("next3"),this.onPushClick(),l.moveNext()},onPrevClick:()=>{console.log("prev"),l.movePrevious()}}}]),l.drive())},onPushClick(){const e={path:"/fusion/sub/noviceGuidePage2"};r.F.subRouterQuery(e)}}},p=c,u=o(3236),v=(0,u.A)(p,n,i,!1,null,"ae9151e4",null),a=v.exports}}]);